/**
 * Controls Component
 *
 * Main control panel with date navigation, toggles, actions, and timeline
 */

import React from 'react';
import DateNavigation from './DateNavigation';
import ViewToggle from './ViewToggle';
import RouteToggle from './RouteToggle';
import RealTimeToggle from './RealTimeToggle';
import Timeline from '../Timeline';

const Controls = ({
    // Date navigation props
    currentDate,
    onDateNavigate,
    isDateChanging,

    // View toggle props
    isSatelliteView,
    onSatelliteToggle,
    isHighZoomFallback,

    // Route toggle props
    showRoutes,
    onRouteToggle,

    // Distance unit props
    distanceUnit,
    onDistanceUnitToggle,

    // Actions
    onClearMarkers,
    onRefresh,
    onShowMarkerCustomization,

    // Real-time tracking props
    realTimeEnabled,
    onRealTimeToggle,
    autoFollowEnabled,
    onAutoFollowToggle,
    isRealTimeConnected,
    realTimeStatus,
    realTimeError,

    // Stats
    stats,

    // Timeline props
    timelineEvents,
    selectedPoints,
    onPointSelect,
    onNotesClick,
    onCenterClick,
    distanceCalculations,
    directDistance
}) => {
    return (
        <div style={{
            position: 'absolute',
            top: '20px',
            right: '20px',
            zIndex: 1000,
            display: 'flex',
            flexDirection: 'column',
            gap: '16px',
            width: '380px',
            boxSizing: 'border-box'
        }}>
            {/* Main Control Panel Container */}
            <div style={{
                backgroundColor: 'rgba(255, 255, 255, 0.98)',
                backdropFilter: 'blur(12px)',
                border: '1px solid rgba(0, 0, 0, 0.08)',
                borderRadius: '16px',
                padding: '8px',
                boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15), 0 4px 16px rgba(0, 0, 0, 0.1)',
                width: '100%',
                boxSizing: 'border-box'
            }}>
                {/* Date Navigation Section */}
                <div style={{
                    marginBottom: '6px',
                    paddingBottom: '6px',
                    borderBottom: '1px solid rgba(0, 0, 0, 0.06)'
                }}>
                    <DateNavigation
                        currentDate={currentDate}
                        onNavigate={onDateNavigate}
                        isChanging={isDateChanging}
                    />
                </div>

                {/* Real-Time Tracking Section */}
                <div style={{
                    marginBottom: '6px',
                    paddingBottom: '6px',
                    borderBottom: '1px solid rgba(0, 0, 0, 0.06)'
                }}>
                    <RealTimeToggle
                        realTimeEnabled={realTimeEnabled}
                        onRealTimeToggle={onRealTimeToggle}
                        autoFollowEnabled={autoFollowEnabled}
                        onAutoFollowToggle={onAutoFollowToggle}
                        isConnected={isRealTimeConnected}
                        connectionStatus={realTimeStatus}
                        error={realTimeError}
                        currentDate={currentDate}
                    />
                </div>

                {/* Toolbar Section */}
                <div style={{
                    marginBottom: '6px',
                    paddingBottom: '6px',
                    borderBottom: '1px solid rgba(0, 0, 0, 0.06)'
                }}>
                    <div className="panel-section-title">
                        🛠️ Toolbar
                    </div>
                    <div className="button-grid-3">
                        <ViewToggle
                            isSatelliteView={isSatelliteView}
                            onToggle={onSatelliteToggle}
                            isHighZoomFallback={isHighZoomFallback}
                        />

                        <RouteToggle
                            showRoutes={showRoutes}
                            onToggle={onRouteToggle}
                        />

                        <button
                            onClick={onDistanceUnitToggle}
                            className="ui-button outline"
                            title={`Switch to ${distanceUnit === 'km' ? 'miles' : 'kilometers'}`}
                        >
                            📏 {distanceUnit.toUpperCase()}
                        </button>
                    </div>
                </div>

                {/* Action Buttons Section */}
                <div style={{
                    marginBottom: stats ? '6px' : '0',
                    paddingBottom: stats ? '6px' : '0',
                    borderBottom: stats ? '1px solid rgba(0, 0, 0, 0.06)' : 'none'
                }}>
                    <div className="panel-section-title">
                        ⚡ Actions
                    </div>
                    <div className="button-grid-2">
                        <button
                            onClick={onClearMarkers}
                            className="ui-button danger"
                            title="Clear all markers from map"
                        >
                            🗑️ Clear
                        </button>

                        <button
                            onClick={() => {
                                console.log('🔄 Refresh button clicked in Controls component');
                                onRefresh();
                            }}
                            className="ui-button primary"
                            title="Refresh GPS data"
                        >
                            🔄 Refresh
                        </button>

                        <button
                            onClick={onShowMarkerCustomization}
                            className="ui-button secondary"
                            style={{ gridColumn: 'span 2' }}
                            title="Customize marker appearance"
                        >
                            🎯 Markers
                        </button>
                    </div>
                </div>

                {/* Stats Section */}
                {stats && (
                    <div style={{
                        marginBottom: '12px',
                        paddingBottom: '10px',
                        borderBottom: '1px solid rgba(0, 0, 0, 0.06)'
                    }}>
                        <div className="panel-section-title">
                            📊 Statistics
                        </div>
                        <div className="stats-display">
                            📏 {stats.distance} {stats.unit}
                        </div>
                    </div>
                )}

                {/* Timeline Section - Integrated into the same frame */}
                <div>
                    <div className="panel-section-title">
                        📍 Timeline
                    </div>
                    <div style={{
                        maxHeight: '400px',
                        overflowY: 'auto',
                        marginTop: '8px'
                    }}>
                        <Timeline
                            timelineEvents={timelineEvents}
                            selectedPoints={selectedPoints}
                            onPointSelect={onPointSelect}
                            onNotesClick={onNotesClick}
                            onCenterClick={onCenterClick}
                            distanceCalculations={distanceCalculations}
                            directDistance={directDistance}
                            isIntegrated={true}
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Controls;
