/**
 * Real-Time Toggle Component
 * 
 * Toggle control for enabling/disabling real-time GPS tracking
 * Shows connection status and provides manual control
 */

import React from 'react';

const RealTimeToggle = ({
    realTimeEnabled,
    onRealTimeToggle,
    isConnected,
    connectionStatus,
    error,
    currentDate,
    autoFollowEnabled = true,
    onAutoFollowToggle = null
}) => {
    // Only show for today's view
    const isToday = currentDate === 'today';
    
    if (!isToday) {
        return null;
    }

    const getStatusColor = () => {
        if (error) return '#ff4444';
        if (isConnected) return '#00ff00';
        if (connectionStatus === 'reconnecting') return '#ffaa00';
        return '#888888';
    };

    const getStatusText = () => {
        if (error) return 'Error';
        if (isConnected) return 'Live';
        if (connectionStatus === 'reconnecting') return 'Reconnecting';
        if (connectionStatus === 'connecting') return 'Connecting';
        return 'Offline';
    };

    return (
        <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '8px 12px',
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            borderRadius: '6px',
            border: '1px solid #ddd',
            fontSize: '14px',
            fontWeight: '500',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
            {/* Real-time toggle switch */}
            <label style={{
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                cursor: 'pointer',
                userSelect: 'none'
            }}>
                <div style={{
                    position: 'relative',
                    width: '40px',
                    height: '20px',
                    backgroundColor: realTimeEnabled ? '#4CAF50' : '#ccc',
                    borderRadius: '10px',
                    transition: 'background-color 0.3s ease'
                }}>
                    <div style={{
                        position: 'absolute',
                        top: '2px',
                        left: realTimeEnabled ? '22px' : '2px',
                        width: '16px',
                        height: '16px',
                        backgroundColor: 'white',
                        borderRadius: '50%',
                        transition: 'left 0.3s ease',
                        boxShadow: '0 1px 3px rgba(0,0,0,0.3)'
                    }} />
                </div>
                <input
                    type="checkbox"
                    checked={realTimeEnabled}
                    onChange={(e) => onRealTimeToggle(e.target.checked)}
                    style={{ display: 'none' }}
                />
                <span style={{ color: '#333' }}>Real-time</span>
            </label>

            {/* Connection status indicator */}
            <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '4px'
            }}>
                <div style={{
                    width: '8px',
                    height: '8px',
                    borderRadius: '50%',
                    backgroundColor: getStatusColor(),
                    animation: isConnected ? 'pulse 2s infinite' : 'none'
                }} />
                <span style={{
                    color: getStatusColor(),
                    fontSize: '12px',
                    fontWeight: '600'
                }}>
                    {getStatusText()}
                </span>
            </div>

            {/* Auto-follow toggle (only show when real-time is enabled) */}
            {realTimeEnabled && onAutoFollowToggle && (
                <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px',
                    marginLeft: '8px',
                    paddingLeft: '8px',
                    borderLeft: '1px solid #ddd'
                }}>
                    <label style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '4px',
                        cursor: 'pointer',
                        userSelect: 'none'
                    }}>
                        <input
                            type="checkbox"
                            checked={autoFollowEnabled}
                            onChange={(e) => onAutoFollowToggle(e.target.checked)}
                            style={{
                                width: '12px',
                                height: '12px',
                                cursor: 'pointer'
                            }}
                        />
                        <span style={{
                            color: '#666',
                            fontSize: '11px',
                            fontWeight: '500'
                        }}>
                            Follow
                        </span>
                    </label>
                </div>
            )}

            {/* Error tooltip */}
            {error && (
                <div style={{
                    position: 'relative',
                    cursor: 'help'
                }} title={error}>
                    <span style={{
                        color: '#ff4444',
                        fontSize: '12px'
                    }}>⚠️</span>
                </div>
            )}

            <style jsx>{`
                @keyframes pulse {
                    0% { opacity: 1; }
                    50% { opacity: 0.5; }
                    100% { opacity: 1; }
                }
            `}</style>
        </div>
    );
};

export default RealTimeToggle;
