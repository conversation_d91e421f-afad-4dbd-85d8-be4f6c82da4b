/**
 * UltraSimpleGPS - Refactored Main Component
 * 
 * This is the main GPS tracking component, now refactored into a clean modular architecture
 * while maintaining the exact same API and functionality as the original monolithic version.
 */

import React, { useState, useEffect, useCallback } from 'react';
import './UltraSimpleGPS.css';
import './components/UILayout.css';
import MarkerCustomization from '../MarkerCustomization';
import DeliveryAssignmentMarkers from '../components/DeliveryAssignmentMarkers';
import RealTimeDebugPanel from './components/RealTimeDebugPanel';

// Import our modular architecture
import {
    useGPSData,
    useMapInstance,
    useMarkerTracking,
    useRouteVisibility
} from './hooks';

import useRealTimeTracking from './hooks/useRealTimeTracking';

import {
    Controls,
    MapContainer
} from './components';

import { MapService } from './services';

import { navigateDate as navigateDateTZ, calculateDirectDistance } from './utils';

import { OLA_MAPS_CONFIG, PERFORMANCE_CONFIG } from './constants';

const UltraSimpleGPS = ({
    agentId = "3b35ff6d-d482-4cb5-bbc1-ed78774c59b2",
    viewerId = "00000000-0000-0000-0000-000000000001", // Default viewer ID - in production this would come from authentication
    onAgentProfileLoad = null
}) => {
    // Core state
    const [currentDate, setCurrentDate] = useState('today');
    const [distanceUnit, setDistanceUnit] = useState('km');
    const [isDateChanging, setIsDateChanging] = useState(false);
    const [selectedPoints, setSelectedPoints] = useState([]);
    const [directDistance, setDirectDistance] = useState(null);
    const [showMarkerCustomization, setShowMarkerCustomization] = useState(false);
    const [customMarkerSettings, setCustomMarkerSettingsState] = useState({});
    const [forceGPSRedraw, setForceGPSRedraw] = useState(0); // Force GPS redraw trigger
    const [realTimeEnabled, setRealTimeEnabled] = useState(true); // Real-time tracking toggle
    const [focusEnabled, setFocusEnabled] = useState(false); // Focus mode toggle
    const [showDebugPanel, setShowDebugPanel] = useState(false); // Debug panel visibility

    // Custom hooks for modular functionality
    const {
        gpsPoints,
        loading,
        agentProfile,
        timelineEvents,
        statusMarkers,
        deliveryMarkers,
        deliveryRoutes,
        stats,
        distanceCalculations,
        lastKnownLocation,
        isMapLoading,
        fetchTrackingData,
        clearData,
        updateDistanceUnit,
        drawGPSLine,
        drawDeliveryRoutes,
        addRealTimeGPSPoint,
        updateGPSPoints
    } = useGPSData(agentId);

    // Set agent ID in MapService for profile photo generation
    useEffect(() => {
        MapService.setAgentId(agentId);
        console.log('🆔 Set agent ID in MapService:', agentId);
    }, [agentId]);

    // Notify parent when agent profile is loaded
    useEffect(() => {
        if (agentProfile && onAgentProfileLoad) {
            onAgentProfileLoad(agentProfile);
        }
    }, [agentProfile, onAgentProfileLoad]);

    // Load and apply marker settings from database
    useEffect(() => {
        const loadMarkerSettings = async () => {
            try {
                const viewerId = '00000000-0000-0000-0000-000000000001'; // Default viewer ID
                console.log('🔄 Loading marker settings for GPS markers...');

                const response = await fetch(`/api/markers/settings/${viewerId}`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.data.settings) {
                        // Convert array to object for easier access
                        const settingsObj = {};
                        data.data.settings.forEach(setting => {
                            settingsObj[setting.marker_type] = setting;
                        });

                        console.log('✅ Loaded marker settings for GPS:', settingsObj);
                        MapService.setMarkerSettings(settingsObj);
                    }
                } else {
                    console.warn('⚠️ Failed to load marker settings, using defaults');
                }
            } catch (error) {
                console.error('❌ Error loading marker settings:', error);
            }
        };

        loadMarkerSettings();
    }, []);

    const {
        isSatelliteView,
        isHighZoomFallback,
        mapRef,
        initializeMap,
        toggleSatelliteView,
        getMapInstance,
        updateMapWithData,
        showLastKnownLocationOnMap
    } = useMapInstance();

    const {
        setCustomMarkerSettings,
        addStatusMarkers,
        addTimelineEventMarkers,
        addDeliveryMarkers,
        clearAllMarkers
    } = useMarkerTracking(getMapInstance());

    const {
        showRoutes,
        toggleRouteVisibility
    } = useRouteVisibility();

    // Real-time location update handler (defined before the hook)
    const handleRealTimeLocationUpdate = useCallback((locationUpdate) => {
        console.log('🎯 Real-time location update:', locationUpdate);

        // Only process real-time updates for today's view
        if (currentDate !== 'today') {
            console.log('⏭️ Ignoring real-time update - not viewing today');
            return;
        }

        try {
            // Update GPS points with new location
            const newGPSPoint = [
                locationUpdate.location.longitude,
                locationUpdate.location.latitude
            ];

            // Add to existing GPS points using the hook method
            addRealTimeGPSPoint(newGPSPoint);

            // Update GPS line in real-time
            const mapInstance = getMapInstance();
            if (mapInstance && mapInstance.gpsRenderer) {
                // Get current GPS points and add the new one
                const currentPoints = [...gpsPoints, newGPSPoint];
                mapInstance.gpsRenderer.updateGPSLineRealTime(newGPSPoint, currentPoints);

                // Add real-time marker
                mapInstance.gpsRenderer.addRealTimeMarker(newGPSPoint, {
                    speed: locationUpdate.speed,
                    heading: locationUpdate.heading,
                    accuracy: locationUpdate.accuracy,
                    battery: locationUpdate.battery
                });

                // Always call smoothFollowLocation - it will handle focus mode internally
                mapInstance.gpsRenderer.smoothFollowLocation(newGPSPoint);
            }



        } catch (error) {
            console.error('❌ Error processing real-time location update:', error);
        }
    }, [currentDate, getMapInstance, addRealTimeGPSPoint, gpsPoints]);

    // Real-time connection change handler
    const handleRealTimeConnectionChange = useCallback((event) => {
        console.log('🔗 Real-time connection status:', event);

        // Enterprise-grade connection monitoring
        switch (event.type) {
            case 'connected':
                console.log('✅ Real-time tracking connected for agent:', agentId);
                // Could trigger analytics event here
                break;
            case 'disconnected':
                console.log('🔌 Real-time tracking disconnected for agent:', agentId);
                // Could show user notification
                break;
            case 'reconnecting':
                console.log(`🔄 Real-time tracking reconnecting (${event.attempt}/${event.maxAttempts})`);
                // Could show reconnection progress
                break;
            default:
                console.log('🔗 Unknown connection event:', event);
        }
    }, [agentId]);

    // Real-time tracking hook (after callback functions are defined)
    const {
        isConnected: isRealTimeConnected,
        connectionStatus: realTimeStatus,
        lastUpdate: realTimeUpdate,
        error: realTimeError,
        connect: connectRealTime,
        disconnect: disconnectRealTime
    } = useRealTimeTracking(agentId, {
        autoConnect: realTimeEnabled && currentDate === 'today', // Only auto-connect for today
        onLocationUpdate: handleRealTimeLocationUpdate,
        onConnectionChange: handleRealTimeConnectionChange,
        enableLogging: true
    });

    // Keyboard shortcuts for debug panel (Ctrl+Shift+D)
    useEffect(() => {
        const handleKeyDown = (event) => {
            if (event.ctrlKey && event.shiftKey && event.key === 'D') {
                event.preventDefault();
                setShowDebugPanel(prev => !prev);
                console.log('🐛 Debug panel toggled:', !showDebugPanel);
            }
        };

        window.addEventListener('keydown', handleKeyDown);
        return () => window.removeEventListener('keydown', handleKeyDown);
    }, [showDebugPanel]);

    // Load custom marker settings (viewer-specific, not agent-specific)
    const loadCustomMarkerSettings = useCallback(async () => {
        try {
            console.log('🎨 Loading custom marker settings for viewer:', viewerId, '(viewing agent:', agentId, ')');
            const response = await fetch(`/api/markers/settings/${viewerId}`);

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.data.settings) {
                    const settingsMap = {};
                    data.data.settings.forEach(setting => {
                        settingsMap[setting.marker_type] = setting;
                    });
                    setCustomMarkerSettings(settingsMap); // For MarkerService
                    setCustomMarkerSettingsState(settingsMap); // For Timeline component

                    // Update GPS renderer with new marker settings
                    MapService.setMarkerSettings(settingsMap);

                    console.log('✅ Viewer-specific custom marker settings loaded:', settingsMap);

                    // Force GPS redraw to apply new marker settings
                    setForceGPSRedraw(prev => prev + 1);
                } else {
                    // No custom settings found, use empty object
                    setCustomMarkerSettings({});
                    setCustomMarkerSettingsState({});
                    MapService.setMarkerSettings({});
                }
            }
        } catch (error) {
            console.error('❌ Error loading custom marker settings:', error);
        }
    }, [viewerId, agentId, setCustomMarkerSettings]);

    // Handle date navigation
    const handleDateNavigate = useCallback(async (direction) => {
        setIsDateChanging(true);

        try {
            const result = navigateDateTZ(currentDate, direction);
            setCurrentDate(result.newDate);

            // Clear existing data and fetch new data
            clearData();
            await fetchTrackingData(result.newDate, distanceUnit);

            // AUTO-REFRESH: After date change, trigger refresh like refresh button
            setTimeout(async () => {
                try {
                    clearData();
                    await fetchTrackingData(result.newDate, distanceUnit);

                    // FORCE GPS LINE DRAWING: Trigger GPS redraw after auto-refresh
                    setTimeout(() => {
                        setForceGPSRedraw(prev => prev + 1); // Increment to trigger useEffect
                    }, 500); // Small delay to ensure data is set

                } catch (refreshError) {
                    console.error('❌ Error during auto-refresh:', refreshError);
                }
            }, 1000); // 1 second delay

            console.log(`📅 Date navigated to: ${result.newDate}`);

        } catch (error) {
            console.error('❌ Error navigating date:', error);
        } finally {
            setTimeout(() => setIsDateChanging(false), PERFORMANCE_CONFIG.DATE_CHANGE_RESET_DELAY);
        }
    }, [currentDate, distanceUnit, clearData, fetchTrackingData]);

    // Handle distance unit toggle
    const handleDistanceUnitToggle = useCallback(() => {
        const newUnit = distanceUnit === 'km' ? 'miles' : 'km';
        setDistanceUnit(newUnit);
        updateDistanceUnit(newUnit);
    }, [distanceUnit, updateDistanceUnit]);

    // Handle point selection for distance calculation and route segment highlighting
    const handlePointSelect = useCallback((index) => {
        setSelectedPoints(prev => {
            if (prev.includes(index)) {
                // Deselect point
                const newSelection = prev.filter(i => i !== index);
                if (newSelection.length < 2) {
                    setDirectDistance(null);
                    // Clear segment highlighting and show full route
                    if (gpsPoints.length > 0) {
                        MapService.clearGPSLine();
                        MapService.drawGPSLine(gpsPoints);
                    }
                }
                return newSelection;
            } else {
                // Select point
                const newSelection = [...prev, index].slice(-2); // Keep only last 2 selections

                if (newSelection.length === 2 && timelineEvents) {
                    // Calculate direct distance between selected points
                    const fromEvent = timelineEvents[newSelection[0]];
                    const toEvent = timelineEvents[newSelection[1]];
                    const distance = calculateDirectDistance(fromEvent, toEvent);
                    setDirectDistance(distance);

                    // 🎯 ADVANCED FEATURE: Highlight route segment between selected points
                    if (gpsPoints.length > 0) {
                        console.log('🎯 Highlighting route segment between timeline points:', newSelection);
                        MapService.highlightRouteSegment(gpsPoints, timelineEvents, newSelection);

                        // 🎯 NEW FEATURE: Auto-center map on route segment
                        setTimeout(() => {
                            MapService.centerOnRouteSegment(gpsPoints, timelineEvents, newSelection);
                        }, 300); // Small delay to let the route render first
                    }
                } else if (newSelection.length === 1 && timelineEvents) {
                    // Only one point selected - show full route and center on that point
                    if (gpsPoints.length > 0) {
                        MapService.clearGPSLine();
                        MapService.drawGPSLine(gpsPoints);
                    }

                    // 🎯 NEW FEATURE: Auto-center map on selected timeline event
                    const selectedEvent = timelineEvents[index];
                    if (selectedEvent) {
                        setTimeout(() => {
                            MapService.centerOnTimelineEvent(selectedEvent, 17);
                        }, 300); // Small delay to let the route render first
                    }
                }

                return newSelection;
            }
        });
    }, [timelineEvents, gpsPoints]);

    // Handle notes click
    const handleNotesClick = useCallback((event) => {
        const locationName = event.address || 'Location not available';
        const eventTime = new Date(event.recorded_at).toLocaleString();
        const eventType = event.event_type || event.type || 'Timeline Event';

        alert(`${eventType}\n\n📍 ${locationName}\n\n🕒 ${eventTime}\n\n📝 ${event.notes || 'No additional notes'}`);
    }, []);

    // Handle center click - center map on timeline event location
    const handleCenterClick = useCallback((event) => {
        console.log('🎯 Center button clicked for event:', event);

        if (!event) {
            console.error('❌ No event provided for center click');
            return;
        }

        // Center map on the timeline event location
        MapService.centerOnTimelineEvent(event, 18); // Higher zoom for center button

        // Optional: Show a brief notification
        const locationName = event.address || `${event.latitude?.toFixed(6)}, ${event.longitude?.toFixed(6)}` || 'Location';
        console.log(`🎯 Centered map on: ${locationName}`);

    }, []);

    // Handle real-time toggle
    const handleRealTimeToggle = useCallback((enabled) => {
        console.log('🔄 Real-time tracking toggled:', enabled);
        setRealTimeEnabled(enabled);

        if (enabled && currentDate === 'today') {
            connectRealTime();
        } else {
            disconnectRealTime();
        }
    }, [currentDate, connectRealTime, disconnectRealTime]);

    // Handle focus toggle
    const handleFocusToggle = useCallback((enabled) => {
        console.log('🎯 Focus mode toggled:', enabled);
        setFocusEnabled(enabled);

        // Update GPS renderer focus mode
        const mapInstance = getMapInstance();
        if (mapInstance && mapInstance.gpsRenderer) {
            mapInstance.gpsRenderer.setFocusMode(enabled);
        }
    }, [getMapInstance]);

    // Handle refresh
    const handleRefresh = useCallback(async () => {
        console.log('🔄 Refresh button clicked!', { currentDate, distanceUnit });
        try {
            console.log('🧹 Clearing data...');
            clearData();
            console.log('📡 Fetching new tracking data...');
            await fetchTrackingData(currentDate, distanceUnit);
            console.log('✅ Refresh completed successfully');
        } catch (error) {
            console.error('❌ Error refreshing data:', error);
        }
    }, [currentDate, distanceUnit, clearData, fetchTrackingData]);

    // Initialize map and load data on mount
    useEffect(() => {
        const initialize = async () => {
            try {
                console.log('🚀 Initializing UltraSimpleGPS...');

                // Wait for mapRef to be available
                if (!mapRef.current) {
                    console.log('⏳ Waiting for map container...');
                    return;
                }

                // Initialize map with safe defaults
                const mapOptions = {
                    center: OLA_MAPS_CONFIG.DEFAULT_CENTER || [77.5946, 12.9716],
                    zoom: OLA_MAPS_CONFIG.DEFAULT_ZOOM || 12
                };

                console.log('🗺️ Initializing map with options:', mapOptions);
                await initializeMap(mapRef.current, mapOptions);

                // Load custom marker settings
                await loadCustomMarkerSettings();

                // Load initial data
                await fetchTrackingData(currentDate, distanceUnit);

                console.log('✅ UltraSimpleGPS initialized successfully');

            } catch (error) {
                console.error('❌ Error initializing UltraSimpleGPS:', error);
                // Don't throw - let the component render with error state
            }
        };

        const timer = setTimeout(initialize, PERFORMANCE_CONFIG.MAP_INIT_DELAY || 100);
        return () => clearTimeout(timer);
    }, [agentId, initializeMap, loadCustomMarkerSettings, fetchTrackingData, currentDate, distanceUnit]); // Include all dependencies

    // Update map when data changes
    useEffect(() => {
        const mapInstance = getMapInstance();
        if (mapInstance && (gpsPoints.length > 0 || timelineEvents.length > 0 || statusMarkers.length > 0)) {
            updateMapWithData(gpsPoints, timelineEvents, statusMarkers);
        }
    }, [gpsPoints, timelineEvents, statusMarkers, getMapInstance, updateMapWithData]);

    // Add markers when data changes
    useEffect(() => {
        if (statusMarkers.length > 0) {
            addStatusMarkers(statusMarkers);
        }
    }, [statusMarkers, addStatusMarkers]);

    useEffect(() => {
        if (timelineEvents.length > 0) {
            addTimelineEventMarkers(timelineEvents);
        }
    }, [timelineEvents, addTimelineEventMarkers]);

    useEffect(() => {
        if (deliveryMarkers.length > 0) {
            addDeliveryMarkers(deliveryMarkers);
        }
    }, [deliveryMarkers, addDeliveryMarkers]);

    // Show last known location if available
    useEffect(() => {
        if (lastKnownLocation) {
            showLastKnownLocationOnMap(lastKnownLocation);
        }
    }, [lastKnownLocation, showLastKnownLocationOnMap]);

    // Draw GPS line when GPS points change or force redraw is triggered
    useEffect(() => {
        const mapInstance = getMapInstance();
        if (mapInstance && gpsPoints && gpsPoints.length > 0) {
            drawGPSLine(mapInstance);
        }
    }, [gpsPoints, getMapInstance, drawGPSLine, currentDate, forceGPSRedraw]);

    // Draw delivery routes when routes change or route visibility toggles
    useEffect(() => {
        const mapInstance = getMapInstance();
        if (mapInstance && deliveryRoutes.length > 0) {
            console.log(`🛣️ Drawing ${deliveryRoutes.length} delivery routes (showRoutes: ${showRoutes})`);
            drawDeliveryRoutes(mapInstance, showRoutes);
        }
    }, [deliveryRoutes, showRoutes, getMapInstance, drawDeliveryRoutes]);

    return (
        <div style={{ width: '100%', height: '100vh', display: 'flex' }}>
            {/* Map Container */}
            <MapContainer
                mapRef={mapRef}
                isMapLoading={isMapLoading}
                loadingMessage={loading ? 'Loading GPS data...' : 'Initializing map...'}
            >
                {/* Controls with integrated Timeline */}
                <Controls
                    currentDate={currentDate}
                    onDateNavigate={handleDateNavigate}
                    isDateChanging={isDateChanging}
                    isSatelliteView={isSatelliteView}
                    onSatelliteToggle={toggleSatelliteView}
                    isHighZoomFallback={isHighZoomFallback}
                    showRoutes={showRoutes}
                    onRouteToggle={toggleRouteVisibility}
                    distanceUnit={distanceUnit}
                    onDistanceUnitToggle={handleDistanceUnitToggle}
                    onClearMarkers={clearAllMarkers}
                    onRefresh={handleRefresh}
                    onShowMarkerCustomization={() => setShowMarkerCustomization(true)}
                    realTimeEnabled={realTimeEnabled}
                    onRealTimeToggle={handleRealTimeToggle}
                    focusEnabled={focusEnabled}
                    onFocusToggle={handleFocusToggle}
                    isRealTimeConnected={isRealTimeConnected}
                    realTimeStatus={realTimeStatus}
                    realTimeError={realTimeError}
                    stats={stats}
                    timelineEvents={timelineEvents}
                    selectedPoints={selectedPoints}
                    onPointSelect={handlePointSelect}
                    onNotesClick={handleNotesClick}
                    onCenterClick={handleCenterClick}
                    distanceCalculations={distanceCalculations}
                    directDistance={directDistance}
                />
            </MapContainer>

            {/* Delivery Assignment Markers */}
            <DeliveryAssignmentMarkers
                map={getMapInstance()}
                agentId={agentId}
                currentLocation={gpsPoints.length > 0 ? {
                    latitude: gpsPoints[gpsPoints.length - 1].latitude,
                    longitude: gpsPoints[gpsPoints.length - 1].longitude
                } : null}
            />

            {/* Marker Customization Modal */}
            {showMarkerCustomization && (
                <MarkerCustomization
                    viewerId={viewerId}
                    agentId={agentId}
                    onClose={() => setShowMarkerCustomization(false)}
                    onSave={loadCustomMarkerSettings}
                />
            )}

            {/* Real-Time Debug Panel (Ctrl+Shift+D to toggle) */}
            <RealTimeDebugPanel
                isVisible={showDebugPanel}
                onClose={() => setShowDebugPanel(false)}
                agentId={agentId}
            />
        </div>
    );
};

export default UltraSimpleGPS;
