/**
 * <PERSON><PERSON><PERSON><PERSON> - Handles GPS line drawing, arrows, and markers
 */

import { MapCleaner } from './MapCleaner';
import { MapUtils } from './MapUtils';

export class GPSRenderer {
    constructor(mapInstance, agentId = null) {
        this.mapInstance = mapInstance;
        this.cleaner = new MapCleaner(mapInstance);
        this.isDrawingGPSLine = false;
        this.arrowUpdateTimeout = null;
        this.agentId = agentId;
        this.markerSettings = {}; // Store marker settings from database

        // Focus mode - simpler and more reliable than interaction tracking
        this.focusMode = false;
        this.focusZoomLevel = 16;
        this.autoZoomEnabled = true;

        // Smooth icon movement system (like delivery apps)
        this.currentIconPosition = null;
        this.targetIconPosition = null;
        this.iconAnimationId = null;
        this.iconMovementSpeed = 0;
        this.lastGPSTime = null;

        // Debug logging setup
        this.setupDebugLogging();
    }

    /**
     * Setup comprehensive debug logging to track all map movements
     */
    setupDebugLogging() {
        if (!this.mapInstance) return;

        console.log('🐛 DEBUG: Setting up comprehensive map movement logging');

        // Track all zoom events
        this.mapInstance.on('zoomstart', (e) => {
            const zoom = this.mapInstance.getZoom();
            console.log('🐛 DEBUG: ZOOM START - Current zoom:', zoom, 'Focus mode:', this.focusMode);
        });

        this.mapInstance.on('zoom', (e) => {
            const zoom = this.mapInstance.getZoom();
            console.log('🐛 DEBUG: ZOOM CHANGE - New zoom:', zoom, 'Focus mode:', this.focusMode);
        });

        this.mapInstance.on('zoomend', (e) => {
            const zoom = this.mapInstance.getZoom();
            console.log('🐛 DEBUG: ZOOM END - Final zoom:', zoom, 'Focus mode:', this.focusMode);
        });

        // Track all move events
        this.mapInstance.on('movestart', (e) => {
            const center = this.mapInstance.getCenter();
            console.log('🐛 DEBUG: MOVE START - Center:', center, 'Focus mode:', this.focusMode);
        });

        this.mapInstance.on('move', (e) => {
            const center = this.mapInstance.getCenter();
            const zoom = this.mapInstance.getZoom();
            console.log('🐛 DEBUG: MOVE - Center:', center, 'Zoom:', zoom, 'Focus mode:', this.focusMode);
        });

        this.mapInstance.on('moveend', (e) => {
            const center = this.mapInstance.getCenter();
            const zoom = this.mapInstance.getZoom();
            console.log('🐛 DEBUG: MOVE END - Final center:', center, 'Final zoom:', zoom, 'Focus mode:', this.focusMode);
        });

        // Track data source changes
        this.mapInstance.on('sourcedata', (e) => {
            if (e.sourceId === 'gps-line') {
                console.log('🐛 DEBUG: GPS LINE SOURCE DATA CHANGED - Source:', e.sourceId, 'Focus mode:', this.focusMode);
            }
        });
    }

    /**
     * Enable focus mode - locks camera to agent at current zoom level
     * @param {boolean} enabled - Whether to enable focus mode
     */
    setFocusMode(enabled) {
        this.focusMode = enabled;

        if (enabled) {
            // Store current zoom level when focus is enabled
            this.focusZoomLevel = this.mapInstance.getZoom();
            console.log('🎯 Focus mode ENABLED - locked at zoom level:', this.focusZoomLevel);
        } else {
            console.log('🎯 Focus mode DISABLED');
        }
    }

    /**
     * Check if focus mode is enabled
     * @returns {boolean} True if focus mode is enabled
     */
    isFocusModeEnabled() {
        return this.focusMode;
    }

    /**
     * Add arrow image to map for GPS direction arrows
     */
    addArrowImage() {
        if (!this.mapInstance) return;

        // Create a simple arrow SVG
        const arrowSvg = `
            <svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path d="M10 2 L18 10 L14 10 L14 18 L6 18 L6 10 L2 10 Z" 
                      fill="#1976D2" 
                      stroke="#ffffff" 
                      stroke-width="1"/>
            </svg>
        `;

        const img = new Image();
        img.onload = () => {
            if (!this.mapInstance.hasImage('simple-arrow')) {
                this.mapInstance.addImage('simple-arrow', img);
            }
        };
        img.src = 'data:image/svg+xml;base64,' + btoa(arrowSvg);
    }

    /**
     * Draw GPS tracking line with arrows (blue line like old version)
     * @param {Array} gpsPoints - GPS coordinates [[lng, lat], ...]
     * @param {boolean} enableAutoZoom - Whether to auto-zoom to the route (default: true)
     */
    drawGPSLine(gpsPoints, enableAutoZoom = true) {
        console.log('🐛 DEBUG: drawGPSLine CALLED - Points:', gpsPoints.length, 'EnableAutoZoom:', enableAutoZoom, 'Focus mode:', this.focusMode);

        if (!this.mapInstance || !gpsPoints || gpsPoints.length === 0) {
            console.log('⚠️ Cannot draw GPS line: missing map or points');
            return;
        }

        // Log the call stack to see what's calling this function
        console.log('🐛 DEBUG: drawGPSLine CALL STACK:', new Error().stack);

        // Prevent rapid duplicate drawing with a short debounce
        if (this.isDrawingGPSLine) {
            setTimeout(() => this.drawGPSLine(gpsPoints), 200);
            return;
        }

        this.isDrawingGPSLine = true;

        try {
            // COMPREHENSIVE CLEANUP: Remove ALL existing GPS-related layers and sources
            this.cleaner.clearGPSLineCompletely();

            // Wait for cleanup to complete before adding new layers
            setTimeout(() => {
                try {
                    // Add GPS line source
                    this.mapInstance.addSource('gps-line', {
                        type: 'geojson',
                        data: {
                            type: 'Feature',
                            geometry: {
                                type: 'LineString',
                                coordinates: gpsPoints
                            }
                        }
                    });

                    // Add GPS path layer (thick blue line like Google Maps)
                    this.mapInstance.addLayer({
                        id: 'gps-path',
                        type: 'line',
                        source: 'gps-line',
                        paint: {
                            'line-color': '#1976D2',
                            'line-width': [
                                'interpolate',
                                ['linear'],
                                ['zoom'],
                                10, 4,   // 4px at zoom 10
                                14, 6,   // 6px at zoom 14
                                18, 8    // 8px at zoom 18
                            ],
                            'line-opacity': 0.9
                        },
                        layout: {
                            'line-cap': 'round',
                            'line-join': 'round'
                        }
                    });

                    // Auto-center and zoom to user's last coordinate (COMPLETELY DISABLED FOR DEBUG)
                    console.log('🐛 DEBUG: Auto-zoom check - EnableAutoZoom:', enableAutoZoom, 'Points:', gpsPoints.length, 'Focus mode:', this.focusMode);
                    console.log('🐛 DEBUG: AUTO-ZOOM COMPLETELY DISABLED FOR DEBUGGING');

                    // TEMPORARILY DISABLED ALL AUTO-ZOOM
                    /*
                    if (enableAutoZoom && gpsPoints.length > 0 && !this.focusMode) {
                        const lastCoordinate = gpsPoints[gpsPoints.length - 1];
                        const currentZoom = this.mapInstance.getZoom();

                        console.log('🐛 DEBUG: PERFORMING AUTO-ZOOM - From zoom:', currentZoom, 'To zoom: 16, Center:', lastCoordinate);

                        this.mapInstance.easeTo({
                            center: lastCoordinate,
                            zoom: 16,
                            duration: 1500
                        });
                        console.log('🎯 Auto-zoomed to last GPS coordinate');
                    } else if (gpsPoints.length > 0) {
                        console.log('🎯 Auto-zoom prevented - focus mode enabled or auto-zoom disabled');
                        console.log('🐛 DEBUG: Auto-zoom prevented - EnableAutoZoom:', enableAutoZoom, 'Focus mode:', this.focusMode);
                    }
                    */

                    // Add directional arrows
                    try {
                        this.addDirectionalArrows(gpsPoints);
                    } catch (arrowError) {
                        console.error('❌ Error adding arrows:', arrowError);
                    }

                    // Add start and end markers
                    try {
                        this.addStartEndMarkers(gpsPoints);
                    } catch (markerError) {
                        console.error('❌ Error adding start/end markers:', markerError);
                    }

                } catch (error) {
                    console.error('❌ Error in delayed GPS line drawing:', error);
                } finally {
                    this.isDrawingGPSLine = false;
                }
            }, 50); // Small delay to ensure cleanup completes

        } catch (error) {
            console.error('❌ Error drawing GPS line:', error);
            this.isDrawingGPSLine = false;
        }
    }

    /**
     * Update GPS line with new point for real-time tracking
     * @param {Array} newPoint - New GPS coordinate [lng, lat]
     * @param {Array} allPoints - All GPS points including the new one
     */
    updateGPSLineRealTime(newPoint, allPoints) {
        console.log('🐛 DEBUG: updateGPSLineRealTime CALLED - New point:', newPoint, 'Total points:', allPoints.length, 'Focus mode:', this.focusMode);

        if (!this.mapInstance || !newPoint || !allPoints) {
            console.log('⚠️ Cannot update GPS line: missing data');
            return;
        }

        try {
            console.log('🎯 Updating GPS line with real-time point:', newPoint);

            // Check if GPS line source exists
            const source = this.mapInstance.getSource('gps-line');
            if (source) {
                // Update existing source with new data
                source.setData({
                    type: 'Feature',
                    geometry: {
                        type: 'LineString',
                        coordinates: allPoints
                    }
                });

                // Update arrows with new points
                this.updateDirectionalArrowsRealTime(allPoints);

                console.log('✅ GPS line updated with real-time data');
            } else {
                // If source doesn't exist, draw the full line without auto-zoom for real-time updates
                console.log('🐛 DEBUG: GPS source not found, calling drawGPSLine with autoZoom=false');
                console.log('🔄 GPS source not found, drawing full line without auto-zoom');
                this.drawGPSLine(allPoints, false); // Disable auto-zoom for real-time updates
            }

        } catch (error) {
            console.error('❌ Error updating GPS line in real-time:', error);
        }
    }

    /**
     * Update directional arrows for real-time GPS updates
     * @param {Array} points - All GPS coordinates
     */
    updateDirectionalArrowsRealTime(points) {
        if (!points || points.length < 3) {
            return;
        }

        try {
            // Clear existing arrows timeout
            if (this.arrowUpdateTimeout) {
                clearTimeout(this.arrowUpdateTimeout);
            }

            // Debounce arrow updates to avoid too frequent updates
            this.arrowUpdateTimeout = setTimeout(() => {
                this.cleaner.clearDirectionalArrows();
                this.createGoogleStyleArrows(points);
            }, 200); // 200ms debounce

        } catch (error) {
            console.error('❌ Error updating arrows in real-time:', error);
        }
    }

    /**
     * Add animated marker for real-time location
     * @param {Array} coordinates - [lng, lat]
     * @param {Object} options - Marker options
     */
    addRealTimeMarker(coordinates, options = {}) {
        if (!this.mapInstance || !coordinates) {
            return;
        }

        try {
            const markerId = 'real-time-marker';

            // Remove existing real-time marker
            this.cleaner.clearRealTimeMarker();

            // Add source for real-time marker
            this.mapInstance.addSource(markerId, {
                type: 'geojson',
                data: {
                    type: 'Feature',
                    geometry: {
                        type: 'Point',
                        coordinates: coordinates
                    },
                    properties: {
                        title: 'Live Location',
                        timestamp: new Date().toISOString(),
                        ...options
                    }
                }
            });

            // Add animated marker layer
            this.mapInstance.addLayer({
                id: markerId,
                type: 'circle',
                source: markerId,
                paint: {
                    'circle-radius': [
                        'interpolate',
                        ['linear'],
                        ['zoom'],
                        10, 6,
                        14, 8,
                        18, 12
                    ],
                    'circle-color': '#00ff00',
                    'circle-stroke-width': 2,
                    'circle-stroke-color': '#ffffff',
                    'circle-opacity': 0.8
                }
            });

            // Add pulsing animation layer
            this.mapInstance.addLayer({
                id: `${markerId}-pulse`,
                type: 'circle',
                source: markerId,
                paint: {
                    'circle-radius': [
                        'interpolate',
                        ['linear'],
                        ['zoom'],
                        10, 12,
                        14, 16,
                        18, 24
                    ],
                    'circle-color': '#00ff00',
                    'circle-opacity': [
                        'interpolate',
                        ['linear'],
                        ['get', 'timestamp'],
                        0, 0.6,
                        1000, 0
                    ]
                }
            });

            console.log('✅ Added real-time marker at:', coordinates);

        } catch (error) {
            console.error('❌ Error adding real-time marker:', error);
        }
    }

    /**
     * Add Google Maps style directional arrows to GPS line
     * @param {Array} points - GPS coordinates [[lng, lat], ...]
     */
    addDirectionalArrows(points) {
        if (!points || points.length < 3) {
            console.log('⚠️ Not enough points for arrows');
            return;
        }

        try {
            // Clear existing arrows
            this.cleaner.clearDirectionalArrows();

            // Create professional Google Maps style arrows
            this.createGoogleStyleArrows(points);

            // Update arrows on zoom change
            this.mapInstance.on('zoom', () => {
                this.updateArrowsForZoom(points);
            });

            console.log('✅ Added Google Maps style directional arrows');

        } catch (error) {
            console.error('❌ Error adding directional arrows:', error);
        }
    }

    /**
     * Create Google Maps style arrows with proper spacing and density
     * @param {Array} points - GPS coordinates [[lng, lat], ...]
     */
    createGoogleStyleArrows(points) {
        const zoom = this.mapInstance.getZoom();

        // Much more aggressive spacing - very few arrows at low zoom
        let arrowSpacingMeters;
        if (zoom >= 18) {
            arrowSpacingMeters = 50;   // Very dense for maximum zoom
        } else if (zoom >= 16) {
            arrowSpacingMeters = 100;  // Dense arrows for high zoom
        } else if (zoom >= 14) {
            arrowSpacingMeters = 300;  // Medium density
        } else if (zoom >= 12) {
            arrowSpacingMeters = 800;  // Much sparser
        } else if (zoom >= 10) {
            arrowSpacingMeters = 1500; // Very sparse
        } else {
            arrowSpacingMeters = 3000; // Extremely sparse - only a few arrows visible
        }

        const arrowPoints = [];
        let accumulatedDistance = 0;
        let nextArrowDistance = arrowSpacingMeters;

        for (let i = 0; i < points.length - 1; i++) {
            const current = points[i];
            const next = points[i + 1];
            
            const segmentDistance = MapUtils.calculateDistance(current, next);
            
            // Check if we should place an arrow in this segment
            while (accumulatedDistance + segmentDistance >= nextArrowDistance) {
                const distanceIntoSegment = nextArrowDistance - accumulatedDistance;
                const ratio = distanceIntoSegment / segmentDistance;
                
                // Interpolate position along the segment
                const arrowLng = current[0] + (next[0] - current[0]) * ratio;
                const arrowLat = current[1] + (next[1] - current[1]) * ratio;
                const arrowPosition = [arrowLng, arrowLat];

                // Find direction point (look ahead for better arrow direction)
                let directionPoint = next;
                const lookAheadIndex = Math.min(i + 5, points.length - 1);
                if (lookAheadIndex > i + 1) {
                    directionPoint = points[lookAheadIndex];
                }

                if (!directionPoint) {
                    const fallbackIndex = Math.min(i + 10, points.length - 1);
                    directionPoint = points[fallbackIndex];
                }

                // Calculate correct bearing using proper geographic calculation
                const bearing = MapUtils.calculateCorrectBearing(arrowPosition, directionPoint);

                // Validate bearing - fix MapBox null error
                if (bearing === null || bearing === undefined || isNaN(bearing)) {
                    continue;
                }

                // Fix arrow rotation
                const correctedBearing = bearing;

                // Validate all values before adding to MapBox
                if (MapUtils.validateCoordinates(arrowPosition) && 
                    MapUtils.validateBearing(correctedBearing) &&
                    MapUtils.validateZoom(zoom)) {
                    
                    arrowPoints.push({
                        type: 'Feature',
                        geometry: {
                            type: 'Point',
                            coordinates: arrowPosition
                        },
                        properties: {
                            bearing: correctedBearing,
                            zoom: zoom
                        }
                    });
                }

                // Set next arrow position
                nextArrowDistance += arrowSpacingMeters;
            }
            
            accumulatedDistance += segmentDistance;
        }

        // Add arrow source and layer
        this.mapInstance.addSource('gps-arrows', {
            type: 'geojson',
            data: {
                type: 'FeatureCollection',
                features: arrowPoints
            }
        });

        // Add arrow layer with crisp, clear rendering
        this.mapInstance.addLayer({
            id: 'gps-direction',
            type: 'symbol',
            source: 'gps-arrows',
            layout: {
                'icon-image': 'simple-arrow',
                'icon-size': [
                    'interpolate',
                    ['linear'],
                    ['zoom'],
                    8,  0.7,   // Slightly larger for better visibility
                    10, 0.9,   // Medium-small
                    12, 1.1,   // Medium
                    14, 1.3,   // Medium-large
                    16, 1.5,   // Large
                    18, 1.7,   // Very large
                    20, 2.0    // Maximum - larger for clarity
                ],
                'icon-rotate': ['get', 'bearing'],
                'icon-rotation-alignment': 'map',
                'icon-allow-overlap': true,
                'icon-ignore-placement': true,
                'icon-pitch-alignment': 'map'
            },
            paint: {
                'icon-opacity': 1.0,
                'icon-halo-color': '#ffffff',
                'icon-halo-width': 0.5
            }
        });

        console.log(`✅ Created ${arrowPoints.length} arrows spaced at ${arrowSpacingMeters}m intervals for zoom ${zoom.toFixed(1)}`);
    }

    /**
     * Update arrows when zoom level changes
     * @param {Array} points - GPS coordinates [[lng, lat], ...]
     */
    updateArrowsForZoom(points) {
        // Debounce zoom updates
        if (this.arrowUpdateTimeout) {
            clearTimeout(this.arrowUpdateTimeout);
        }

        this.arrowUpdateTimeout = setTimeout(() => {
            this.cleaner.clearDirectionalArrows();
            this.createGoogleStyleArrows(points);
        }, 150); // Small delay to avoid too frequent updates
    }

    /**
     * Add start and end markers to GPS line with user profile photos
     * @param {Array} points - GPS coordinates [[lng, lat], ...]
     */
    addStartEndMarkers(points) {
        if (!points || points.length < 2) {
            console.log('⚠️ Not enough points for start/end markers');
            return;
        }

        try {
            // AGGRESSIVE CLEANUP: Clear all existing GPS markers
            this.cleaner.clearStartEndMarkers();

            // Also clear any old profile markers array
            if (this.profileMarkers) {
                this.profileMarkers.forEach(marker => {
                    if (marker._cleanup) {
                        marker._cleanup();
                    } else if (marker.parentNode) {
                        marker.parentNode.removeChild(marker);
                    }
                });
                this.profileMarkers = [];
            }

            const startPoint = points[0];
            const endPoint = points[points.length - 1];

            // Add start marker using database settings
            this.addStartMarker(startPoint);

            // Add end marker using database settings
            this.addEndMarker(endPoint);

            console.log('✅ Added start and end markers with profile photos to GPS line');

        } catch (error) {
            console.error('❌ Error adding start/end markers:', error);
        }
    }

    /**
     * Set marker settings from database
     * @param {Object} settings - Marker settings object
     */
    setMarkerSettings(settings) {
        this.markerSettings = settings || {};
        console.log('🎯 GPS Renderer marker settings updated:', this.markerSettings);
    }

    /**
     * Add configurable start marker using database settings
     * @param {Array} coordinates - [lng, lat]
     */
    addStartMarker(coordinates) {
        const startSetting = this.markerSettings.gps_start;

        if (startSetting && startSetting.icon_image_url && startSetting.icon_image_url.trim() !== '') {
            // Use custom image from database
            this.addCustomImageMarker(coordinates, 'start', startSetting.color || '#00aa44', startSetting.icon_image_url);
        } else {
            // Use icon from database or default
            const icon = startSetting?.icon_text || '🏠';
            const color = startSetting?.color || '#00aa44';
            this.addIconMarker(coordinates, 'start', color, icon);
        }
    }

    /**
     * Add configurable end marker using database settings
     * @param {Array} coordinates - [lng, lat]
     */
    addEndMarker(coordinates) {
        const endSetting = this.markerSettings.gps_end;

        if (endSetting && endSetting.icon_image_url && endSetting.icon_image_url.trim() !== '') {
            // Use custom image from database
            this.addCustomImageMarker(coordinates, 'end', endSetting.color || '#F44336', endSetting.icon_image_url);
        } else if (endSetting?.icon_text === '👤' || !endSetting?.icon_text) {
            // Default behavior: show profile photo for end marker
            this.addProfileMarker(coordinates, 'end', endSetting?.color || '#F44336');
        } else {
            // Use custom icon from database
            const icon = endSetting.icon_text;
            const color = endSetting.color || '#F44336';
            this.addIconMarker(coordinates, 'end', color, icon);
        }
    }

    /**
     * Add icon-based start marker
     * @param {Array} coordinates - [lng, lat]
     * @param {string} type - 'start' or 'end'
     * @param {string} borderColor - Border color for the marker
     * @param {string} icon - Icon to display
     */
    addIconMarker(coordinates, type, borderColor, icon) {
        const markerElement = document.createElement('div');
        markerElement.className = `custom-marker ${type}-marker gps-${type}-marker`;
        markerElement.style.cssText = `
            position: absolute;
            width: 48px;
            height: 48px;
            background-color: ${borderColor};
            border: 4px solid white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
            box-shadow: 0 3px 12px rgba(0,0,0,0.4);
            cursor: pointer;
            z-index: 1000;
            transform: translate(-50%, -50%);
        `;

        markerElement.innerHTML = icon;

        this.positionAndTrackMarker(markerElement, coordinates);
    }

    /**
     * Add custom image marker
     * @param {Array} coordinates - [lng, lat]
     * @param {string} type - 'start' or 'end'
     * @param {string} borderColor - Border color for the marker
     * @param {string} imageUrl - Custom image URL
     */
    addCustomImageMarker(coordinates, type, borderColor, imageUrl) {
        const markerElement = document.createElement('div');
        markerElement.className = `custom-marker ${type}-marker gps-${type}-marker`;
        markerElement.style.cssText = `
            position: absolute;
            width: 48px;
            height: 48px;
            border: 4px solid ${borderColor};
            border-radius: 50%;
            overflow: hidden;
            box-shadow: 0 3px 12px rgba(0,0,0,0.4);
            cursor: pointer;
            z-index: 1000;
            transform: translate(-50%, -50%);
            background: white;
        `;

        const agentInitials = this.getAgentInitials();
        markerElement.innerHTML = `
            <img src="${imageUrl}"
                 style="width: 100%; height: 100%; object-fit: cover; display: block;"
                 onerror="this.style.display='none'; this.parentElement.style.backgroundColor='${borderColor}'; this.parentElement.innerHTML='<div style=&quot;display: flex; align-items: center; justify-content: center; width: 100%; height: 100%; color: white; font-weight: bold; font-size: 16px;&quot;>${agentInitials}</div>';" />
        `;

        this.positionAndTrackMarker(markerElement, coordinates);
    }

    /**
     * Add profile marker at specific coordinates
     * @param {Array} coordinates - [lng, lat]
     * @param {string} type - 'start' or 'end'
     * @param {string} borderColor - Border color for the marker
     */
    addProfileMarker(coordinates, type, borderColor) {
        const markerElement = document.createElement('div');
        markerElement.className = `custom-marker ${type}-marker gps-${type}-marker`;
        markerElement.style.cssText = `
            position: absolute;
            width: 48px;
            height: 48px;
            border: 4px solid ${borderColor};
            border-radius: 50%;
            overflow: hidden;
            box-shadow: 0 3px 12px rgba(0,0,0,0.4);
            cursor: pointer;
            z-index: 1000;
            transform: translate(-50%, -50%);
            background: white;
        `;

        // Generate profile photo URL
        const profilePhotoUrl = this.generateProfilePhotoUrl(this.agentId);

        const agentInitials = this.getAgentInitials();
        markerElement.innerHTML = `
            <img src="${profilePhotoUrl}"
                 style="width: 100%; height: 100%; object-fit: cover; display: block;"
                 onerror="this.style.display='none'; this.parentElement.style.backgroundColor='${borderColor}'; this.parentElement.innerHTML='<div style=&quot;display: flex; align-items: center; justify-content: center; width: 100%; height: 100%; color: white; font-weight: bold; font-size: 16px;&quot;>${agentInitials}</div>';" />
        `;

        this.positionAndTrackMarker(markerElement, coordinates);
    }

    /**
     * Position marker on map and track its position during map interactions
     * @param {HTMLElement} markerElement - The marker DOM element
     * @param {Array} coordinates - [lng, lat]
     */
    positionAndTrackMarker(markerElement, coordinates) {
        // Position marker on map using DOM positioning (like existing markers)
        const mapContainer = this.mapInstance.getContainer();

        // Convert coordinates to pixel position
        const point = this.mapInstance.project(coordinates);

        // Position the marker element
        markerElement.style.left = `${point.x}px`;
        markerElement.style.top = `${point.y}px`;

        // Add to map container
        mapContainer.appendChild(markerElement);

        // Store marker element for cleanup
        if (!this.profileMarkers) {
            this.profileMarkers = [];
        }
        this.profileMarkers.push(markerElement);

        // Update marker position when map moves
        const updateMarkerPosition = () => {
            const newPoint = this.mapInstance.project(coordinates);
            markerElement.style.left = `${newPoint.x}px`;
            markerElement.style.top = `${newPoint.y}px`;
        };

        // Listen for map events to update marker position
        this.mapInstance.on('move', updateMarkerPosition);
        this.mapInstance.on('zoom', updateMarkerPosition);

        // Store cleanup function
        markerElement._cleanup = () => {
            this.mapInstance.off('move', updateMarkerPosition);
            this.mapInstance.off('zoom', updateMarkerPosition);
            if (markerElement.parentNode) {
                markerElement.parentNode.removeChild(markerElement);
            }
        };
    }

    /**
     * Generate profile photo URL for agent
     * @param {string} agentId - Agent ID
     * @returns {string} Profile photo URL
     */
    generateProfilePhotoUrl(agentId) {
        if (!agentId) return 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face';

        // Generate a consistent photo based on agent ID
        const photoIndex = Math.abs(agentId.split('-')[0].charCodeAt(0)) % 10;
        return `https://images.unsplash.com/photo-150700321116${photoIndex}?w=100&h=100&fit=crop&crop=face`;
    }

    /**
     * Get agent initials for fallback display
     * @returns {string} Agent initials
     */
    getAgentInitials() {
        if (!this.agentId) return 'AG';

        // Generate initials from agent ID
        const parts = this.agentId.split('-');
        if (parts.length >= 2) {
            return (parts[0].charAt(0) + parts[1].charAt(0)).toUpperCase();
        }
        return this.agentId.substring(0, 2).toUpperCase();
    }

    /**
     * Draw route segment highlighting between two timeline points
     * @param {Array} allGpsPoints - All GPS coordinates [[lng, lat], ...]
     * @param {Array} timelineEvents - Timeline events with coordinates
     * @param {Array} selectedIndices - [startIndex, endIndex] of selected timeline points
     */
    drawRouteSegmentHighlight(allGpsPoints, timelineEvents, selectedIndices) {
        if (!this.mapInstance || !allGpsPoints || !timelineEvents || selectedIndices.length !== 2) {
            return;
        }

        try {
            const [startIndex, endIndex] = selectedIndices.sort((a, b) => a - b);
            const startEvent = timelineEvents[startIndex];
            const endEvent = timelineEvents[endIndex];

            if (!startEvent || !endEvent) return;

            // Find GPS points between the two timeline events
            const segmentPoints = this.extractRouteSegment(allGpsPoints, startEvent, endEvent);

            if (segmentPoints.length < 2) return;

            // Clear existing GPS line
            this.cleaner.clearGPSLineCompletely();

            // Add full route as transparent background
            this.addTransparentRoute(allGpsPoints);

            // Add highlighted segment
            this.addHighlightedSegment(segmentPoints);

            // Add arrows only for highlighted segment
            this.addDirectionalArrows(segmentPoints);

            // Add start and end markers for segment
            this.addSegmentMarkers(segmentPoints, startEvent, endEvent);

            console.log(`✅ Route segment highlighted: ${segmentPoints.length} points between timeline events ${startIndex}-${endIndex}`);

        } catch (error) {
            console.error('❌ Error drawing route segment highlight:', error);
        }
    }

    /**
     * Extract GPS points between two timeline events
     * @param {Array} allGpsPoints - All GPS coordinates
     * @param {Object} startEvent - Start timeline event
     * @param {Object} endEvent - End timeline event
     * @returns {Array} GPS points for the segment
     */
    extractRouteSegment(allGpsPoints, startEvent, endEvent) {
        const startTime = new Date(startEvent.recorded_at).getTime();
        const endTime = new Date(endEvent.recorded_at).getTime();

        // For now, use coordinate proximity to find segment
        // In a real implementation, you'd match by timestamp
        const startCoord = [parseFloat(startEvent.longitude), parseFloat(startEvent.latitude)];
        const endCoord = [parseFloat(endEvent.longitude), parseFloat(endEvent.latitude)];

        // Find closest GPS points to timeline events
        let startGpsIndex = 0;
        let endGpsIndex = allGpsPoints.length - 1;

        let minStartDist = Infinity;
        let minEndDist = Infinity;

        allGpsPoints.forEach((point, index) => {
            const distToStart = this.calculateDistance(point, startCoord);
            const distToEnd = this.calculateDistance(point, endCoord);

            if (distToStart < minStartDist) {
                minStartDist = distToStart;
                startGpsIndex = index;
            }

            if (distToEnd < minEndDist) {
                minEndDist = distToEnd;
                endGpsIndex = index;
            }
        });

        // Ensure correct order
        if (startGpsIndex > endGpsIndex) {
            [startGpsIndex, endGpsIndex] = [endGpsIndex, startGpsIndex];
        }

        return allGpsPoints.slice(startGpsIndex, endGpsIndex + 1);
    }

    /**
     * Add transparent background route
     * @param {Array} allGpsPoints - All GPS coordinates
     */
    addTransparentRoute(allGpsPoints) {
        this.mapInstance.addSource('gps-line-background', {
            type: 'geojson',
            data: {
                type: 'Feature',
                geometry: {
                    type: 'LineString',
                    coordinates: allGpsPoints
                }
            }
        });

        this.mapInstance.addLayer({
            id: 'gps-path-background',
            type: 'line',
            source: 'gps-line-background',
            paint: {
                'line-color': '#CCCCCC',
                'line-width': [
                    'interpolate',
                    ['linear'],
                    ['zoom'],
                    10, 2,   // Thinner for background
                    14, 3,
                    18, 4
                ],
                'line-opacity': 0.3  // Very transparent
            },
            layout: {
                'line-cap': 'round',
                'line-join': 'round'
            }
        });
    }

    /**
     * Add highlighted route segment
     * @param {Array} segmentPoints - GPS coordinates for highlighted segment
     */
    addHighlightedSegment(segmentPoints) {
        this.mapInstance.addSource('gps-line-highlight', {
            type: 'geojson',
            data: {
                type: 'Feature',
                geometry: {
                    type: 'LineString',
                    coordinates: segmentPoints
                }
            }
        });

        this.mapInstance.addLayer({
            id: 'gps-path-highlight',
            type: 'line',
            source: 'gps-line-highlight',
            paint: {
                'line-color': '#1976D2',  // Bright blue for highlight
                'line-width': [
                    'interpolate',
                    ['linear'],
                    ['zoom'],
                    10, 6,   // Thicker for highlight
                    14, 8,
                    18, 10
                ],
                'line-opacity': 1.0  // Fully opaque
            },
            layout: {
                'line-cap': 'round',
                'line-join': 'round'
            }
        });
    }

    /**
     * Add segment start/end markers
     * @param {Array} segmentPoints - GPS coordinates for segment
     * @param {Object} startEvent - Start timeline event
     * @param {Object} endEvent - End timeline event
     */
    addSegmentMarkers(segmentPoints, startEvent, endEvent) {
        const startPoint = segmentPoints[0];
        const endPoint = segmentPoints[segmentPoints.length - 1];

        const markerFeatures = [
            {
                type: 'Feature',
                geometry: {
                    type: 'Point',
                    coordinates: startPoint
                },
                properties: {
                    type: 'segment-start',
                    title: `Start: ${startEvent.event_type || 'Event'}`
                }
            },
            {
                type: 'Feature',
                geometry: {
                    type: 'Point',
                    coordinates: endPoint
                },
                properties: {
                    type: 'segment-end',
                    title: `End: ${endEvent.event_type || 'Event'}`
                }
            }
        ];

        this.mapInstance.addSource('gps-segment-markers', {
            type: 'geojson',
            data: {
                type: 'FeatureCollection',
                features: markerFeatures
            }
        });

        this.mapInstance.addLayer({
            id: 'gps-segment-markers',
            type: 'circle',
            source: 'gps-segment-markers',
            paint: {
                'circle-radius': 10,
                'circle-color': [
                    'case',
                    ['==', ['get', 'type'], 'segment-start'], '#4CAF50',  // Green for start
                    ['==', ['get', 'type'], 'segment-end'], '#F44336',    // Red for end
                    '#2196F3' // Default blue
                ],
                'circle-stroke-color': '#ffffff',
                'circle-stroke-width': 3,
                'circle-opacity': 1.0
            }
        });
    }

    /**
     * Calculate distance between two points (simple Euclidean for proximity)
     * @param {Array} point1 - [lng, lat]
     * @param {Array} point2 - [lng, lat]
     * @returns {number} Distance
     */
    calculateDistance(point1, point2) {
        const dx = point1[0] - point2[0];
        const dy = point1[1] - point2[1];
        return Math.sqrt(dx * dx + dy * dy);
    }

    /**
     * Reset GPS line drawing flag (for error recovery)
     */
    resetGPSLineDrawingFlag() {
        this.isDrawingGPSLine = false;
    }

    /**
     * Focus on real-time location - locks camera to agent position
     * @param {Array} coordinates - [lng, lat] coordinates to focus on
     */
    focusOnLocation(coordinates, locationData = {}) {
        console.log('🎯 FOCUS: Smooth tracking to coordinates:', coordinates, 'Focus mode:', this.focusMode);

        if (!this.mapInstance || !coordinates || !this.focusMode) {
            console.log('🎯 FOCUS: Skipped - Missing requirements');
            return;
        }

        try {
            // Use the stored focus zoom level (locked when focus was enabled)
            const currentZoom = this.mapInstance.getZoom();
            const targetZoom = this.focusZoomLevel || currentZoom;

            // Calculate smooth animation duration based on distance and speed
            const currentCenter = this.mapInstance.getCenter();
            const distance = this.calculateDistance(
                [currentCenter.lng, currentCenter.lat],
                coordinates
            );

            // Dynamic duration based on distance and speed for realistic movement
            const speed = locationData.speed || 1.5; // m/s
            const baseDuration = Math.min(Math.max(distance * 50000, 400), 1200); // 400-1200ms
            const speedFactor = Math.max(0.5, Math.min(2.0, speed / 2)); // Speed adjustment
            const duration = baseDuration / speedFactor;

            console.log('🎯 FOCUS: Smooth tracking - Distance:', distance.toFixed(6), 'Speed:', speed, 'Duration:', duration);

            // Ultra-smooth camera movement that follows the agent naturally
            this.mapInstance.easeTo({
                center: coordinates,
                zoom: targetZoom,        // Maintain locked zoom level
                duration: duration,      // Dynamic duration for realistic movement
                easing: this.createSmoothEasing(), // Custom easing for natural movement
                essential: true
            });

            console.log('🎯 FOCUS: Smoothly tracking agent at locked zoom:', targetZoom);
        } catch (error) {
            console.error('❌ Error in focus tracking:', error);
        }
    }

    /**
     * Create smooth easing function for natural camera movement
     * @returns {Function} Custom easing function
     */
    createSmoothEasing() {
        // Custom cubic-bezier easing for smooth, natural movement
        // Similar to what professional delivery apps use
        return (t) => {
            // Smooth acceleration and deceleration
            return t < 0.5
                ? 4 * t * t * t
                : 1 - Math.pow(-2 * t + 2, 3) / 2;
        };
    }

    /**
     * Smoothly pan to follow real-time location without changing zoom (legacy method)
     * @param {Array} coordinates - [lng, lat] coordinates to follow
     */
    /**
     * Start smooth icon movement to new GPS point (like delivery apps)
     * @param {Array} coordinates - [lng, lat] target coordinates
     * @param {Object} locationData - GPS data with speed, heading, etc.
     */
    smoothFollowLocation(coordinates, locationData = {}) {
        console.log('🚗 DELIVERY-STYLE: Starting smooth icon movement to:', coordinates);

        if (!this.mapInstance || !coordinates) {
            console.log('🚗 DELIVERY-STYLE: Skipped - Missing requirements');
            return;
        }

        // Set target position for smooth icon movement
        this.targetIconPosition = coordinates;

        // If this is the first GPS point, place icon immediately
        if (!this.currentIconPosition) {
            this.currentIconPosition = coordinates;
            this.updateIconPosition(coordinates);
            this.panCameraToIcon(coordinates);
            console.log('🚗 DELIVERY-STYLE: First GPS point - placed icon immediately');
            return;
        }

        // Calculate movement parameters
        const speed = locationData.speed || 1.5; // m/s
        const distance = this.calculateDistance(this.currentIconPosition, coordinates);
        const currentTime = Date.now();

        // Calculate realistic movement duration based on actual speed
        const estimatedDuration = (distance * 111000) / speed * 1000; // Convert to milliseconds
        const clampedDuration = Math.min(Math.max(estimatedDuration, 1000), 8000); // 1-8 seconds

        console.log('🚗 DELIVERY-STYLE: Distance:', distance.toFixed(6), 'Speed:', speed, 'Duration:', clampedDuration);

        // Start smooth icon animation
        this.startSmoothIconMovement(coordinates, clampedDuration, speed);
    }

    /**
     * Start smooth icon movement animation (like Uber/DoorDash)
     * @param {Array} targetCoords - Target coordinates
     * @param {number} duration - Animation duration in ms
     * @param {number} speed - Movement speed in m/s
     */
    startSmoothIconMovement(targetCoords, duration, speed) {
        // Cancel any existing animation
        if (this.iconAnimationId) {
            cancelAnimationFrame(this.iconAnimationId);
        }

        const startCoords = [...this.currentIconPosition];
        const startTime = Date.now();

        console.log('🚗 DELIVERY-STYLE: Starting smooth movement from', startCoords, 'to', targetCoords);

        const animateIcon = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Use smooth easing for natural movement
            const easedProgress = this.createDeliveryAppEasing()(progress);

            // Interpolate position
            const currentLng = startCoords[0] + (targetCoords[0] - startCoords[0]) * easedProgress;
            const currentLat = startCoords[1] + (targetCoords[1] - startCoords[1]) * easedProgress;
            const currentPosition = [currentLng, currentLat];

            // Update icon position
            this.currentIconPosition = currentPosition;
            this.updateIconPosition(currentPosition);

            // Pan camera to follow icon smoothly
            this.panCameraToIcon(currentPosition);

            // Continue animation if not finished
            if (progress < 1) {
                this.iconAnimationId = requestAnimationFrame(animateIcon);
            } else {
                console.log('🚗 DELIVERY-STYLE: Smooth movement completed');
                this.iconAnimationId = null;
            }
        };

        // Start the animation
        this.iconAnimationId = requestAnimationFrame(animateIcon);
    }

    /**
     * Update the visual position of the moving icon
     * @param {Array} coordinates - [lng, lat] coordinates
     */
    updateIconPosition(coordinates) {
        // Update the real-time marker position
        try {
            // Remove existing real-time marker
            if (this.mapInstance.getLayer('realtime-marker')) {
                this.mapInstance.removeLayer('realtime-marker');
            }
            if (this.mapInstance.getSource('realtime-marker')) {
                this.mapInstance.removeSource('realtime-marker');
            }

            // Add updated real-time marker at new position
            this.mapInstance.addSource('realtime-marker', {
                type: 'geojson',
                data: {
                    type: 'Feature',
                    geometry: {
                        type: 'Point',
                        coordinates: coordinates
                    }
                }
            });

            this.mapInstance.addLayer({
                id: 'realtime-marker',
                type: 'circle',
                source: 'realtime-marker',
                paint: {
                    'circle-radius': 8,
                    'circle-color': '#4CAF50',
                    'circle-stroke-width': 3,
                    'circle-stroke-color': '#ffffff',
                    'circle-opacity': 0.9
                }
            });

        } catch (error) {
            console.error('❌ Error updating icon position:', error);
        }
    }

    /**
     * Pan camera to follow the moving icon smoothly
     * @param {Array} coordinates - [lng, lat] coordinates
     */
    panCameraToIcon(coordinates) {
        try {
            // Smooth camera pan to follow icon (no zoom change)
            this.mapInstance.easeTo({
                center: coordinates,
                duration: 100, // Very quick pan to stay with icon
                essential: true
            });
        } catch (error) {
            console.error('❌ Error panning camera to icon:', error);
        }
    }

    /**
     * Create delivery app style easing for natural automatic tracking
     * @returns {Function} Professional easing function
     */
    createDeliveryAppEasing() {
        // Professional easing similar to Uber/DoorDash automatic tracking
        return (t) => {
            // Smooth ease-out for natural automatic following
            return 1 - Math.pow(1 - t, 3);
        };
    }
}
