#!/usr/bin/env node

/**
 * GPS Simulator Script
 * 
 * Simulates realistic GPS tracking for testing real-time updates
 * Creates natural movement patterns like walking, driving, or delivery routes
 */

const axios = require('axios');

// Configuration
const CONFIG = {
    // Backend API URL
    API_URL: process.env.API_URL || 'http://localhost:8081',
    
    // Agent to simulate (you can change this)
    AGENT_ID: '3b35ff6d-d482-4cb5-bbc1-ed78774c59b2',
    
    // Simulation settings
    DURATION_SECONDS: 30, // How long to run simulation
    UPDATE_INTERVAL_MS: 2000, // Send GPS update every 2 seconds
    
    // Movement settings
    MOVEMENT_SPEED_MPS: 1.5, // 1.5 meters per second (walking speed)
    MAX_DIRECTION_CHANGE: 30, // Max degrees to change direction
    
    // Starting location (Bangalore, India - you can change this)
    START_LOCATION: {
        latitude: 12.9716,
        longitude: 77.5946
    }
};

// Movement patterns
const MOVEMENT_PATTERNS = {
    WALKING: {
        speed: 1.5, // m/s
        directionChange: 45,
        description: 'Walking pace with natural turns'
    },
    CYCLING: {
        speed: 4.0, // m/s
        directionChange: 20,
        description: 'Cycling speed with gentle turns'
    },
    DRIVING: {
        speed: 8.0, // m/s (city driving)
        directionChange: 15,
        description: 'City driving with road-like movement'
    },
    DELIVERY: {
        speed: 2.5, // m/s
        directionChange: 60,
        description: 'Delivery agent with frequent stops and turns'
    }
};

class GPSSimulator {
    constructor() {
        this.currentLocation = { ...CONFIG.START_LOCATION };
        this.currentHeading = Math.random() * 360; // Random initial direction
        this.isRunning = false;
        this.updateCount = 0;
        this.startTime = null;
        this.pattern = MOVEMENT_PATTERNS.WALKING; // Default pattern
    }

    /**
     * Convert meters to approximate degrees
     * @param {number} meters - Distance in meters
     * @param {number} latitude - Current latitude for longitude calculation
     * @returns {Object} - {latDelta, lngDelta}
     */
    metersToDegreesApprox(meters, latitude) {
        // Approximate conversion (good enough for simulation)
        const latDelta = meters / 111320; // 1 degree lat ≈ 111.32 km
        const lngDelta = meters / (111320 * Math.cos(latitude * Math.PI / 180));
        
        return { latDelta, lngDelta };
    }

    /**
     * Calculate new position based on current heading and speed
     * @param {number} timeElapsedSeconds - Time since last update
     * @returns {Object} - New location {latitude, longitude}
     */
    calculateNewPosition(timeElapsedSeconds) {
        // Distance traveled in meters
        const distanceMeters = this.pattern.speed * timeElapsedSeconds;
        
        // Convert to degrees
        const { latDelta, lngDelta } = this.metersToDegreesApprox(distanceMeters, this.currentLocation.latitude);
        
        // Calculate new position based on heading
        const headingRad = this.currentHeading * Math.PI / 180;
        const newLatitude = this.currentLocation.latitude + (latDelta * Math.cos(headingRad));
        const newLongitude = this.currentLocation.longitude + (lngDelta * Math.sin(headingRad));
        
        return {
            latitude: newLatitude,
            longitude: newLongitude
        };
    }

    /**
     * Update heading with natural variation
     */
    updateHeading() {
        // Random direction change within limits
        const maxChange = this.pattern.directionChange;
        const directionChange = (Math.random() - 0.5) * 2 * maxChange;
        this.currentHeading = (this.currentHeading + directionChange) % 360;
        
        // Ensure heading is positive
        if (this.currentHeading < 0) {
            this.currentHeading += 360;
        }
    }

    /**
     * Generate realistic GPS data
     * @returns {Object} - GPS data payload
     */
    generateGPSData() {
        const now = new Date();
        
        return {
            agent_id: CONFIG.AGENT_ID,
            role: 'agent',
            trip_id: null,
            location: {
                latitude: this.currentLocation.latitude,
                longitude: this.currentLocation.longitude
            },
            timestamp: now.toISOString(),
            speed_m_s: this.pattern.speed + (Math.random() - 0.5) * 0.5, // Add slight variation
            heading_deg: this.currentHeading,
            accuracy_m: 5 + Math.random() * 10, // 5-15 meter accuracy
            altitude_m: 800 + Math.random() * 50, // Approximate altitude for Bangalore
            battery_pct: Math.max(20, 100 - (this.updateCount * 2)), // Simulate battery drain
            source: 'simulation'
        };
    }

    /**
     * Send GPS data to backend
     * @param {Object} gpsData - GPS data to send
     */
    async sendGPSUpdate(gpsData) {
        try {
            const response = await axios.post(`${CONFIG.API_URL}/api/location/ping`, gpsData, {
                headers: {
                    'Content-Type': 'application/json'
                },
                timeout: 5000
            });

            if (response.status === 200) {
                console.log(`✅ GPS Update ${this.updateCount}: ${gpsData.location.latitude.toFixed(6)}, ${gpsData.location.longitude.toFixed(6)} (Speed: ${gpsData.speed_m_s.toFixed(1)} m/s, Heading: ${gpsData.heading_deg.toFixed(0)}°)`);
            } else {
                console.error(`❌ Failed to send GPS update: ${response.status}`);
            }
        } catch (error) {
            console.error(`❌ Error sending GPS update:`, error.message);
        }
    }

    /**
     * Set movement pattern
     * @param {string} patternName - Pattern name (WALKING, CYCLING, DRIVING, DELIVERY)
     */
    setMovementPattern(patternName) {
        if (MOVEMENT_PATTERNS[patternName]) {
            this.pattern = MOVEMENT_PATTERNS[patternName];
            console.log(`🚶 Movement pattern set to: ${patternName} - ${this.pattern.description}`);
        } else {
            console.error(`❌ Unknown movement pattern: ${patternName}`);
        }
    }

    /**
     * Start GPS simulation
     * @param {string} movementPattern - Movement pattern to use
     */
    async start(movementPattern = 'WALKING') {
        if (this.isRunning) {
            console.log('⚠️ Simulation is already running');
            return;
        }

        this.setMovementPattern(movementPattern);
        this.isRunning = true;
        this.startTime = Date.now();
        this.updateCount = 0;

        console.log('🚀 Starting GPS Simulation...');
        console.log(`📍 Starting location: ${this.currentLocation.latitude.toFixed(6)}, ${this.currentLocation.longitude.toFixed(6)}`);
        console.log(`⏱️ Duration: ${CONFIG.DURATION_SECONDS} seconds`);
        console.log(`📡 Update interval: ${CONFIG.UPDATE_INTERVAL_MS}ms`);
        console.log(`🎯 Agent ID: ${CONFIG.AGENT_ID}`);
        console.log(`🏃 Pattern: ${movementPattern} (${this.pattern.description})`);
        console.log('─'.repeat(80));

        // Send initial position
        await this.sendGPSUpdate(this.generateGPSData());
        this.updateCount++;

        // Start simulation loop
        const interval = setInterval(async () => {
            const elapsedSeconds = (Date.now() - this.startTime) / 1000;
            
            if (elapsedSeconds >= CONFIG.DURATION_SECONDS) {
                clearInterval(interval);
                this.stop();
                return;
            }

            // Update position and heading
            const timeStep = CONFIG.UPDATE_INTERVAL_MS / 1000;
            this.currentLocation = this.calculateNewPosition(timeStep);
            this.updateHeading();

            // Send GPS update
            await this.sendGPSUpdate(this.generateGPSData());
            this.updateCount++;

        }, CONFIG.UPDATE_INTERVAL_MS);

        // Handle Ctrl+C gracefully
        process.on('SIGINT', () => {
            clearInterval(interval);
            this.stop();
        });
    }

    /**
     * Stop GPS simulation
     */
    stop() {
        if (!this.isRunning) return;

        this.isRunning = false;
        const totalTime = (Date.now() - this.startTime) / 1000;
        
        console.log('─'.repeat(80));
        console.log('🏁 GPS Simulation completed!');
        console.log(`📊 Total updates sent: ${this.updateCount}`);
        console.log(`⏱️ Total time: ${totalTime.toFixed(1)} seconds`);
        console.log(`📍 Final location: ${this.currentLocation.latitude.toFixed(6)}, ${this.currentLocation.longitude.toFixed(6)}`);
        console.log('✅ You can now check the real-time updates in your frontend!');
        
        process.exit(0);
    }
}

// Command line interface
function showHelp() {
    console.log(`
🎯 GPS Simulator - Real-time GPS tracking test script

Usage: node gps-simulator.js [pattern] [duration]

Movement Patterns:
  WALKING  - Walking pace (1.5 m/s) with natural turns
  CYCLING  - Cycling speed (4.0 m/s) with gentle turns  
  DRIVING  - City driving (8.0 m/s) with road-like movement
  DELIVERY - Delivery agent (2.5 m/s) with frequent stops

Examples:
  node gps-simulator.js                    # Default: WALKING for 30 seconds
  node gps-simulator.js DRIVING           # Driving pattern for 30 seconds
  node gps-simulator.js CYCLING 60        # Cycling for 60 seconds

Environment Variables:
  API_URL=http://localhost:8080           # Backend API URL
  
Press Ctrl+C to stop simulation early.
    `);
}

// Main execution
async function main() {
    const args = process.argv.slice(2);
    
    if (args.includes('--help') || args.includes('-h')) {
        showHelp();
        return;
    }

    // Parse command line arguments
    const pattern = args[0] || 'WALKING';
    const duration = parseInt(args[1]) || CONFIG.DURATION_SECONDS;
    
    // Update duration if provided
    CONFIG.DURATION_SECONDS = duration;

    // Validate pattern
    if (!MOVEMENT_PATTERNS[pattern]) {
        console.error(`❌ Invalid movement pattern: ${pattern}`);
        console.log('Valid patterns:', Object.keys(MOVEMENT_PATTERNS).join(', '));
        return;
    }

    // Create and start simulator
    const simulator = new GPSSimulator();
    await simulator.start(pattern);
}

// Run the script
if (require.main === module) {
    main().catch(error => {
        console.error('❌ Simulation failed:', error.message);
        process.exit(1);
    });
}

module.exports = GPSSimulator;
